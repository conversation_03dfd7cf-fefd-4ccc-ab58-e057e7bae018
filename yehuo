引脚
野火K210 AI视觉相机 有两组引脚，一组是具有28个引脚（后面统称引脚组1），另一组具有6个引脚（后面统称引脚组2），如下图所示

野火logo
这些引脚组里的IO引脚可以任意复用成其他外设使用，比如说可以复用成uart,spi,i2c,gpio,gpiohs等。

引脚组1
引脚组1里的IO引脚的电平3.3V的，连接引脚时需要注意引脚的电平是否为3.3V，如果连接1.8V 或者 5V的引脚时需要注意添加电平转换
https://doc.embedfire.com/k210/quick_start/zh/latest/_images/pin1.png
野火logo
引脚组1的IO引脚中 IO_0 , IO_1 在串口例程中作为串口1使用
https://doc.embedfire.com/k210/quick_start/zh/latest/_images/pin2.png
引脚组2
引脚组2里的IO引脚的电平1.8V的，连接引脚时需要注意引脚的电平是否为1.8V，如果连接3.3V 或者 5V的引脚时需要注意添加电平转换.



摄像头
野火K210 AI视觉相机 使用的摄像头像素大小为 200W像素

例程讲解
该例程在 例程的xxx ，可以在 CanMV IDE 中打开

import sensor
import lcd

lcd.init()

sensor.reset(dual_buff=True)
sensor.set_pixformat(sensor.RGB565)
sensor.set_framesize(sensor.QVGA)
sensor.skip_frames(time = 1000)

while True:
    img = sensor.snapshot()
    lcd.display(img)
实验准备
将 野火K210 AI视觉相机 连接到 CanMV IDE

执行程序

运行结果
运行之后，可以看到屏幕上一直显示摄像头捕捉的图像

野火logo
程序分析
import sensor
import lcd

lcd.init()
import sensor ：导入用于控制图像传感器的模块

import lcd ：导入用于控制LCD显示屏的模块

lcd.init() ：初始化LCD显示屏

sensor.reset(dual_buff=True)
sensor.set_pixformat(sensor.RGB565)
sensor.set_framesize(sensor.QVGA)
sensor.skip_frames(time = 1000)
sensor.reset(dual_buff=True) ： 重置图像传感器，并启用双缓冲，以提高图像处理的效率

sensor.set_pixformat(sensor.RGB565) ：设置相机的图像格式为RGB565

sensor.set_framesize(sensor.QVGA) ：设置相机的图像分辨率为QVGA（320x240像素）

sensor.skip_frames(time = 1000) ：跳过1000ms时间内的图像，让相机获得稳定的图像

while True:
    img = sensor.snapshot()
    lcd.display(img)
while True: ：开始一个无限循环

img = sensor.snapshot() ：从图像传感器捕获一帧图像

lcd.display(img) ：将捕获的图像显示在LCD上


屏幕
例程
import lcd
import time

lcd.init()
a=0
while a<4 :
    lcd.clear()
    lcd.rotation(a)
    lcd.draw_string(30, 30, "Embedfire", lcd.WHITE, lcd.BLACK)
    time.sleep(1)
    a=a+1

lcd.clear()
lcd.rotation(0)
lcd.draw_string(140, 100, "Embedfire", lcd.WHITE, lcd.BLACK)
time.sleep(1)

lcd.clear(lcd.WHITE)
lcd.draw_string(140, 100, "Embedfire", lcd.BLACK, lcd.WHITE)
实验准备
将 野火K210 AI视觉相机 连接到 CanMV IDE

执行程序

运行结果
1.图像的左上角出现 Embedfire .此时屏幕没旋转

野火logo
2.图像的右上角出现 Embedfire .此时屏幕顺时针旋转了90度

野火logo
3.图像的右下角出现 Embedfire .此时屏幕顺时针旋转了180度

野火logo
4.图像的左下角出现 Embedfire .此时屏幕顺时针旋转了270度

野火logo
5.图像的中心出现 Embedfire

野火logo
6.图像的颜色和背景都和上图的相反，变成了白底黑字

野火logo
程序分析
import lcd
import time
import lcd ：导入用于控制LCD显示屏的模块。

import time ：导入Python的时间模块，用于实现延时功能。

lcd.init()
a=0
while a<4 :
    lcd.clear()
    lcd.rotation(a)
    lcd.draw_string(30, 30, "Embedfire", lcd.WHITE, lcd.BLACK)
    time.sleep(1)
    a=a+1
lcd.init() : 初始化LCD显示屏

a=0 : 初始化变量a，用作循环计数器。

while a<4 : : 开始一个循环，当a小于4时执行。

lcd.clear() : 清除LCD显示屏上的内容。

lcd.rotation(a) :设置LCD的显示方向，参数a从0到3, 0:不旋转 ，1:顺时针旋转90度 2:顺时针旋转180度 3:顺时针旋转270度

lcd.draw_string(30, 30, "Embedfire", lcd.WHITE, lcd.BLACK) : 在LCD上绘制字符串”Embedfire”，指定文本颜色为白色，背景颜色为黑色，坐标为(30, 30)。

time.sleep(1) : 让程序暂停1秒。

a=a+1 :增加循环计数器a的值。

lcd.clear()
lcd.rotation(0)
lcd.draw_string(140, 100, "Embedfire", lcd.WHITE, lcd.BLACK)
time.sleep(1)
lcd.clear() : 清除LCD显示屏上的内容。

lcd.rotation(0) :将LCD的显示方向恢复为默认值（0度，即正常方向）。

lcd.draw_string(140, 100, "Embedfire", lcd.WHITE, lcd.BLACK) : 在LCD上绘制字符串”Embedfire”，指定文本颜色为白色，背景颜色为黑色，坐标为(140, 100)。

time.sleep(1) : 让程序暂停1秒。

lcd.clear(lcd.WHITE)
lcd.draw_string(140, 100, "Embedfire", lcd.BLACK, lcd.WHITE)
lcd.WHITE : 清除LCD显示屏上的内容，并使用白色填充屏幕。

lcd.draw_string(140, 100, "Embedfire", lcd.BLACK, lcd.WHITE) : 在LCD上绘制字符串”Embedfire”，指定文本颜色为黑色，背景颜色为白色，坐标为(140, 100)。

gpio
General Purpose Input Output （通用输入/输出）简称为 GPIO ，或总线扩展器。 K210上有高速 GPIO( GPIOHS ) 和通用 GPIO 在K210上。

高速 GPIO 为 GPIOHS，共 32 个。具有如下特点：

可配置输入输出信号

每个 IO 具有独立中断源

中断支持边沿触发和电平触发

每个 IO 可以分配到 FPIOA 上 48 个管脚之一

可配置上下拉，或者高阻

通用 GPIO 共 8 个，具有如下特点:

8 个 IO 使用一个中断源

可配置输入输出信号

可配置触发 IO 总中断，边沿触发和电平触发

每个 IO 可以分配到 FPIOA 上 48 个管脚之一

引脚占用
GPIOHS

功能

GPIOHS31

LCD_DC

GPIOHS30

LCD_RST

GPIOHS29

LCD_RD

GPIOHS28

SD_CS

在使用gpio时，请避免使用上述引脚


按键
例程讲解
该例程在 例程的xxx ，可以在 CanMV IDE 中打开

from board import board_info
from fpioa_manager import fm
from maix import GPIO
import time

fm.register(board_info.USER_LED, fm.fpioa.GPIOHS1, force=True)
led = GPIO(GPIO.GPIOHS1, GPIO.OUT)

def test_irq(pin_num):
    led.value(key.value())

fm.register(board_info.BOOT_KEY, fm.fpioa.GPIOHS0, force=True)
key=GPIO(GPIO.GPIOHS0, GPIO.IN, GPIO.PULL_NONE)
key.irq(test_irq, GPIO.IRQ_BOTH, GPIO.WAKEUP_NOT_SUPPORT, 7)


while True:
    time.sleep_ms(500)

key.disirq()
fm.unregister(board_info.BOOT_KEY)
实验准备
将 野火K210 AI视觉相机 连接到 CanMV IDE

执行程序

运行结果
运行之后,按下按键用户灯会变绿，松开后灯会灭

程序分析
from board import board_info
from fpioa_manager import fm
from maix import GPIO
import time
from board import board_info : 从board模块导入board_info对象，其中包含了开发板的相关信息。

from fpioa_manager import fm : 导入fpioa_manager模块中的fm对象，用于管理管脚映射。

from maix import GPIO : 从maix模块导入GPIO类，用于控制通用输入输出。

fm.register(board_info.USER_LED, fm.fpioa.GPIOHS1, force=True)
led = GPIO(GPIO.GPIOHS1, GPIO.OUT)
fm.register(board_info.USER_LED, fm.fpioa.GPIOHS1, force=True) : 将USER_LED对应的管脚映射到GPIOHS1（高速度GPIO）。

led = GPIO(GPIO.GPIOHS1, GPIO.OUT) : 初始化GPIOHS1管脚作为输出，并创建一个名为led的GPIO对象

def test_irq(pin_num):
    led.value(key.value())

fm.register(board_info.BOOT_KEY, fm.fpioa.GPIOHS0, force=True)
key=GPIO(GPIO.GPIOHS0, GPIO.IN, GPIO.PULL_NONE)
key.irq(test_irq, GPIO.IRQ_BOTH, GPIO.WAKEUP_NOT_SUPPORT, 7)
def test_irq(pin_num) : 定义一个中断服务函数test_irq，它接受一个参数pin_num，即触发的GPIO管脚编号。 led.value(key.value()) : 将LED的状态设置为按键的状态。如果按键被按下（低电平），LED灯亮；如果按键释放（高电平），LED灯灭。 fm.register(board_info.BOOT_KEY, fm.fpioa.GPIOHS0, force=True) : 将BOOT_KEY对应的管脚映射到GPIOHS0（高速度GPIO）。 key=GPIO(GPIO.GPIOHS0, GPIO.IN, GPIO.PULL_NONE) : 初始化GPIOHS0管脚作为输入，并且不使用上拉或下拉电阻。 key.irq(test_irq, GPIO.IRQ_BOTH, GPIO.WAKEUP_NOT_SUPPORT, 7) : 为key对象注册中断，当按键被按下或释放时，都会触发中断。中断服务函数为test_irq，不支持下唤醒，中断优先级为7。

for i in range(20):
    time.sleep_ms(500)

key.disirq()
fm.unregister(board_info.BOOT_KEY)
while True: : 开始一个无限循环。

time.sleep_ms(500) : 让程序暂停500毫秒。

key.disirq() : 注销key对象的中断。

fm.unregister(board_info.BOOT_KEY) : 循环结束后，注销BOOT_KEY的管脚映射，释放资源。


定时器
例程讲解
该例程在 例程的xxx ，可以在 CanMV IDE 中打开

import time
import sensor
import lcd
from machine import Timer
from board import board_info
from fpioa_manager import fm
from maix import GPIO

status = 0
fm.register(board_info.USER_LED, fm.fpioa.GPIOHS1, force=True)
led = GPIO(GPIO.GPIOHS1, GPIO.OUT)

def on_timer(timer):
    global status
    status = 0 if (status == 1) else 1
    led.value(status)

timer = Timer(Timer.TIMER0, Timer.CHANNEL0,
            mode=Timer.MODE_PERIODIC, period=200,
            unit=Timer.UNIT_MS, callback=on_timer, arg=None)

lcd.init()
sensor.reset(dual_buff=True)
sensor.set_pixformat(sensor.RGB565)
sensor.set_framesize(sensor.QVGA)
sensor.skip_frames(time = 1000)

try:
    while True:
        img = sensor.snapshot()
        lcd.display(img)

except:
    timer.deinit()
    del timer
实验准备
将 野火K210 AI视觉相机 连接到 CanMV IDE

执行程序

运行结果
1.运行之后， 野火K210 AI视觉相机 背面的绿色led灯每隔300ms会切换亮灭状态 2.在灯闪的同时屏幕也在捕捉摄像头捕捉的图像

程序分析
import time
import sensor
import lcd
from machine import Timer
from board import board_info
from fpioa_manager import fm
from maix import GPIO
from machine import Timer ：导入Timer类，用于创建定时器。

import time ：导入Python的时间模块，尽管在这个脚本中并没有直接使用。

import sensor ：导入用于控制图像传感器的模块。

import lcd ：导入用于控制LCD显示屏的模块。

from board import board_info ：从board模块导入board_info对象，其中包含了开发板的相关信息。

from fpioa_manager import fm ：导入fpioa_manager模块中的fm对象，用于管理管脚映射。

from maix import GPIO ：从maix模块导入GPIO类，用于控制通用输入输出。

status = 0
fm.register(board_info.USER_LED, fm.fpioa.GPIOHS1, force=True)
led = GPIO(GPIO.GPIOHS1, GPIO.OUT)

def on_timer(timer):
    global status
    status = 0 if (status == 1) else 1
    led.value(status)

timer = Timer(Timer.TIMER0, Timer.CHANNEL0,
            mode=Timer.MODE_PERIODIC, period=200,
            unit=Timer.UNIT_MS, callback=on_timer, arg=None)
status = 0 ：初始化LED的状态为关闭。

fm.register(board_info.USER_LED, fm.fpioa.GPIOHS1, force=True) ：将USER_LED对应的管脚映射到GPIOHS1（高速度GPIO）。

led = GPIO(GPIO.GPIOHS1, GPIO.OUT) ：初始化GPIOHS1管脚作为输出，并创建一个名为led的GPIO对象。

def on_timer(timer) ：定义一个定时器回调函数on_timer。

global status 声明status为全局变量。

status = 0 if (status == 1) else 1 ：更新status变量的值，如果当前为1则变为0，否则变为1。

led.value(status) ：设置LED的状态为新的status值。

timer = Timer(Timer.TIMER0, Timer.CHANNEL0, mode=Timer.MODE_PERIODIC, period=200, unit=Timer.UNIT_MS, callback=on_timer, arg=None) ：创建一个周期性定时器，每200毫秒触发一次on_timer回调函数。

lcd.init()
sensor.reset(dual_buff=True)
sensor.set_pixformat(sensor.RGB565)
sensor.set_framesize(sensor.QVGA)
sensor.skip_frames(time = 1000)

try:
    while True:
        img = sensor.snapshot()
        lcd.display(img)

except:
    timer.deinit()
    del timer
lcd.init() ：初始化LCD显示屏。

sensor.reset(dual_buff=True) ：重置图像传感器，并启用双缓冲，以提高图像处理的效率。

sensor.set_pixformat(sensor.RGB565) ：设置图像传感器的像素格式为RGB565。

sensor.set_framesize(sensor.QVGA) ：设置图像传感器的帧大小为QVGA（320x240像素）。

sensor.skip_frames(time = 1000) ：跳过1000毫秒内的帧，等待传感器稳定。

try: ：开始一个try块，用于捕获异常。

while True: ：开始一个无限循环。

img = sensor.snapshot() ：从图像传感器捕获一帧图像。

lcd.display(img) ：将捕获的图像显示在LCD上。

except: 捕获任何在try块中发生的异常。

``timer.deinit() ``：停止定时器。

del timer ：删除定时器对象。

串口
野火K210 AI视觉相机 可以使用28pin IO接口配置两个串口设备，串口的波特率可达5Mbps

野火logo野火logo
如上图IO引脚图，可以随意使用任意IO作为uart_tx 和 uart_rx 串口的电平是3.3V的，如果接5V的引脚需要调整电路再连接。

下面使用 IO_1 和 IO_0 这两个引脚分别复用成 UART1_TX 和 UART1_RX 。

其他引脚也可以复用成 UART1_TX 和 UART1_RX ，需要自行修改配置。

UART2的配置方法也和UART1类似，修改相应名字即可。

例程讲解
该例程在 例程的 02-Hardware\uart.py ，可以在 CanMV IDE 中打开

from board import board_info
from fpioa_manager import fm
from maix import GPIO
import time
from machine import UART
import _thread

fm.register(1, fm.fpioa.UART1_TX)
fm.register(0, fm.fpioa.UART1_RX)

# 构造UART对象
uart1 = UART(UART.UART1, 115200)

def uart_rev_func(name):
    while 1:
        if uart1.any() != 0:
            rev = uart1.read()
            print("UART get rev:", rev.decode())

_thread.start_new_thread(uart_rev_func,("uart_rev",))
while True:
    uart1.write("Send from UART!")
    time.sleep(1)
实验准备
将 野火K210 AI视觉相机 连接到 CanMV IDE

将28pin引脚中的 IO_1 连接到串口模块的 RX

将28pin引脚中的 IO_0 连接到串口模块的 TX

将28pin引脚中的 GND 连接到串口模块的 GND

将串口模块的波特率设置为 115200

执行程序

运行结果
1.运行之后， 每隔一秒就会发送 Send from UART! 到串口模块上。

2.与此同时，程序还设置了一个线程，当串口模块发送数据时，会将串口模块的数据打印到串口终端上。

程序分析
from board import board_info
from fpioa_manager import fm
from maix import GPIO
import time
from machine import UART
import _thread
导入模块 board fpioa_manager maix time machine _thread

fm.register(1, fm.fpioa.UART1_TX)
fm.register(0, fm.fpioa.UART1_RX)

# 构造UART对象
uart1 = UART(UART.UART1, 115200)
配置 IO_1 引脚为 UART1_TX

配置 IO_0 引脚为 UART1_RX

创建一个UART对象uart1，配置为使用UART1接口，波特率为115200

def uart_rev_func(name):
    while 1:
        if uart1.any() != 0:
            rev = uart1.read()
            print("UART get rev:", rev.decode())

_thread.start_new_thread(uart_rev_func,("uart_rev",))
uart_rev_func(name) ：这是一个线程函数，用于接收UART数据。

if uart1.any() != 0: ：如果UART有数据可读。

data = uart1.read() ：读取数据。

print("UART1 get data:", data.decode()) ：将接收到的数据解码并打印。

while True:
    uart1.write("Send from UART!")
    time.sleep(1)
uart1.write("From UART1!") ：向UART发送字符串“From UART1!”